#!/usr/bin/env python3
"""Test script for prompt_scenario_doc function"""

from pfc.core.llms import prompt_scenario_doc

# Test with example problem
problem_description = "Create a text summarization system that can process long documents and generate concise summaries"

requirements = [
    "Must preserve key information and main points",
    "Should maintain original tone and style",
    "Must handle documents up to 10,000 words",
    "Should produce summaries between 100-300 words",
    "Must avoid hallucination or adding new information",
    "Should work with different document types (articles, reports, papers)"
]

print("=== Testing prompt_scenario_doc ===")
print(f"Problem: {problem_description}")
print(f"Requirements: {len(requirements)} items")

# Generate ScenarioDoc
scenario_doc = prompt_scenario_doc(problem_description, requirements)

print(f"\n=== Generated ScenarioDoc ===")
print(f"Version: {scenario_doc.v}")
print(f"Source ReqDoc Version: {scenario_doc.source_req_doc_v}")
print(f"Number of scenarios: {len(scenario_doc.scenarios)}")

print("\n=== Scenarios ===")
for i, scenario in enumerate(scenario_doc.scenarios, 1):
    print(f"\n{i}. {scenario.name} ({scenario.type})")
    print(f"   If: {scenario.if_this}")
    print(f"   Then: {scenario.then_that}")

print("\n=== Test Complete ===")
print("✅ prompt_scenario_doc function works correctly!")