#!/usr/bin/env python3
"""Unified prompt interface for both OpenAI and Fireworks Llama APIs

Basic Usage Examples:

    from pfc.core.llms.oaifireprompt import prompt

    result = prompt("What is 2+2?")
    result = prompt("What is 2+2?", debug=False)
    result = prompt("What is 2+2?", provider="fireworks")

    result = oaiprompt("Tell me a joke")
    result = fireprompt("Write a haiku")

    # Silent mode (no debug output)
    result = prompt("Hello", debug=False)

    # Custom parameters
    result = prompt("Write a story", max_tokens=100, temperature=0.8)

    # Markdown rendering
    prompt("Create a table", render_markdown=True)
"""

import json
import os
import re
from typing import Any

import requests
from dotenv import load_dotenv
from openai import OpenAI
from rich import print as rprint

try:
    from tabulate import tabulate

    HAS_TABULATE = True
except ImportError:
    HAS_TABULATE = False

# Global flag to show tabulate status only once
_TABULATE_STATUS_SHOWN = False

load_dotenv()

__all__ = ["firellama", "fireprompt", "oaiprompt", "prompt", "prompt_full"]

# ========================================================================================================
# FIREWORKS LLAMA API CLIENT
# ========================================================================================================


class FireworksLlama:
    """OpenAI-compatible wrapper for Fireworks Llama API"""

    def __init__(
        self,
        api_key: str = "sta-OhCjDbjeaCQzJpW9EZNpYcp2HJQvEe2Md8o3sDMQDglUSCVU8uBcCSmci6I1",
        base_url: str = "https://aie.invsta.systems/steps/fireworks/v1/chat/completions",
    ):
        self.api_key = api_key
        self.base_url = base_url
        self.headers = {"accept": "application/json", "Authorization": f"Basic {api_key}", "Content-Type": "application/json"}

    def create(
        self,
        messages: list[dict[str, str]],
        model: str = "accounts/fireworks/models/llama-v3p2-90b-vision-instruct",
        temperature: float = 0.7,
        max_tokens: int | None = None,
        top_p: float = 1.0,
        n: int = 1,
        stop: str | list[str] | None = None,
        presence_penalty: float = 0.0,
        frequency_penalty: float = 0.0,
        stream: bool = False,
        response_format: dict[str, str] | None = None,
        seed: int | None = None,
        tools: list[dict] | None = None,
        tool_choice: str | dict | None = None,
        user: str | None = None,
        **kwargs,
    ) -> dict[str, Any]:
        """Create a chat completion with OpenAI-compatible parameters"""

        # Build Fireworks API request
        request_data = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "top_p": top_p,
            "n": n,
            "presence_penalty": presence_penalty,
            "frequency_penalty": frequency_penalty,
        }

        # Add optional parameters if provided
        if max_tokens is not None:
            request_data["max_tokens"] = max_tokens
        if stop is not None:
            request_data["stop"] = stop
        if response_format is not None:
            request_data["response_format"] = response_format
        if seed is not None:
            request_data["seed"] = seed
        if tools is not None:
            request_data["tools"] = tools
        if tool_choice is not None:
            request_data["tool_choice"] = tool_choice
        if user is not None:
            request_data["user"] = user

        # Add any additional kwargs
        request_data.update(kwargs)

        # Wrap in Fireworks-specific format
        data = {"client_name": "default", "request_data": request_data}

        if stream:
            raise NotImplementedError("Streaming is not yet implemented")

        # Make the request
        response = requests.post(self.base_url, headers=self.headers, data=json.dumps(data))

        if response.status_code != 200:
            raise Exception(f"API Error: {response.status_code} - {response.text}")

        return response.json()


def firellama(
    messages: list[dict[str, str]] | None = None,
    model: str = "accounts/fireworks/models/llama-v3p2-90b-vision-instruct",
    api_key: str | None = None,
    prompt: str | None = None,
    temperature: float = 0.1,
    **kwargs,
) -> dict[str, Any]:
    """Quick function to make a Fireworks Llama API call with OpenAI-compatible interface"""
    # If prompt is provided, create simple user message automatically
    if prompt is not None:
        messages = [{"role": "user", "content": prompt}]
    elif messages is None:
        raise ValueError("Either 'prompt' or 'messages' must be provided")

    client = FireworksLlama(api_key=api_key) if api_key else FireworksLlama()
    return client.create(messages=messages, model=model, temperature=temperature, **kwargs)


# ========================================================================================================
# UNIFIED PROMPT FUNCTIONS
# ========================================================================================================


def prompt(
    prompt: str,
    model: str = "gpt-4.1",
    api_key: str | None = None,
    temperature: float = 0.1,
    max_tokens: int | None = None,
    debug: bool = True,
    debug_full: bool = False,
    render_markdown: bool = False,
    provider: str = "openai",
    response_format: dict | None = None,
    **kwargs,
) -> str:
    """
    Unified prompt function for OpenAI and Fireworks - minimal interface

    Args:
        prompt: The prompt string
        model: Model to use
        api_key: API key (uses env var if not provided)
        temperature: Sampling temperature
        max_tokens: Maximum tokens to generate
        debug: Show rich print output (default: True)
        debug_full: Show full prompt/response without truncation (default: False)
        render_markdown: Render markdown in terminal with colors (default: False)
        provider: "openai" or "fireworks" (default: "openai")
        response_format: OpenAI response format dict for structured output (default: None)
        **kwargs: Additional parameters
    """
    if provider == "openai":
        # OpenAI API call
        if api_key is None:
            api_key = os.getenv("OPENAI_API_KEY")
        client = OpenAI(api_key=api_key)
        messages = [{"role": "user", "content": prompt}]
        request_params = {"model": model, "messages": messages, "temperature": temperature, **kwargs}
        if max_tokens:
            request_params["max_tokens"] = max_tokens
        if response_format:
            request_params["response_format"] = response_format
        response = client.chat.completions.create(**request_params)
        content = response.choices[0].message.content

    elif provider == "fireworks":
        # Fireworks API call
        response = firellama(prompt=prompt, model=model, api_key=api_key, temperature=temperature, max_tokens=max_tokens, **kwargs)
        content = response["choices"][0]["message"]["content"]

    else:
        raise ValueError(f"Unsupported provider: {provider}. Use 'openai' or 'fireworks'")

    if render_markdown:
        # Render markdown and print
        rendered = render_markdown_text(content)
        print(rendered)
    elif debug:
        # Normal debug output
        display_prompt = prompt if debug_full else prompt[:150] + ("..." if len(prompt) > 150 else "")
        display_content = content if debug_full else content[:150] + ("..." if len(content) > 150 else "")

        # single vs double line prints / logs. max 150 chars for either, two line output if combined length > 150 or multi-line prompt/content.
        combined_length = len(display_prompt) + len(display_content) + 7  # 7 for " --> " separator
        has_multiline_prompt = "\n" in display_prompt
        has_multiline_content = "\n" in display_content

        if combined_length > 150 or has_multiline_prompt or has_multiline_content:
            rprint(f"[yellow]{display_prompt}[/yellow]")
            rprint(f"--> [green]{display_content}[/green]")
        else:
            rprint(f"[yellow]{display_prompt}[/yellow] --> [green]{display_content}[/green]")

    return content


def prompt_full(
    prompt: str,
    model: str = "gpt-4.1",
    api_key: str | None = None,
    temperature: float = 0.1,
    max_tokens: int | None = None,
    provider: str = "openai",
    response_format: dict | None = None,
    **kwargs,
) -> dict[str, Any]:
    """Same as prompt() but returns full response object"""
    if provider == "openai":
        if api_key is None:
            api_key = os.getenv("OPENAI_API_KEY")
        client = OpenAI(api_key=api_key)
        messages = [{"role": "user", "content": prompt}]
        request_params = {"model": model, "messages": messages, "temperature": temperature, **kwargs}
        if max_tokens is not None:
            request_params["max_tokens"] = max_tokens
        if response_format:
            request_params["response_format"] = response_format
        response = client.chat.completions.create(**request_params)
        return {
            "choices": [
                {
                    "message": {"content": response.choices[0].message.content, "role": response.choices[0].message.role},
                    "finish_reason": response.choices[0].finish_reason,
                }
            ],
            "usage": {
                "prompt_tokens": response.usage.prompt_tokens,
                "completion_tokens": response.usage.completion_tokens,
                "total_tokens": response.usage.total_tokens,
            },
        }
    if provider == "fireworks":
        return firellama(prompt=prompt, model=model, api_key=api_key, temperature=temperature, max_tokens=max_tokens, **kwargs)
    raise ValueError(f"Unsupported provider: {provider}. Use 'openai' or 'fireworks'")


# Convenience aliases
def oaiprompt(prompt_text: str, **kwargs) -> str:
    """OpenAI prompt alias"""
    return prompt(prompt_text, provider="openai", **kwargs)


def fireprompt(prompt_text: str, **kwargs) -> str:
    """Fireworks prompt alias"""
    return prompt(prompt_text, provider="fireworks", **kwargs)



# ========================================================================================================
# MARKDOWN RENDERING UTILITIES
# ========================================================================================================


def parse_markdown_table(text: str) -> list[list[str]]:
    """Extract markdown table data for tabulate rendering"""
    lines = text.split("\n")
    table_lines = []

    for line in lines:
        if "|" in line and line.strip():
            # Skip separator lines (lines with only |, -, :, and spaces)
            clean_line = line.replace("|", "").replace("-", "").replace(":", "").replace(" ", "")
            if not clean_line:
                continue

            # Extract cell contents
            if line.strip().startswith("|") and line.strip().endswith("|"):
                cells = [cell.strip() for cell in line.split("|")[1:-1]]
                if cells and any(cell.strip() for cell in cells):  # At least one non-empty cell
                    table_lines.append(cells)
        elif table_lines:  # We've found some table rows, stop at first non-table line
            break

    return table_lines


def render_markdown_text(text: str, use_tabulate: bool = True) -> str:
    """
    Simple terminal markdown renderer for headers and tables

    Args:
        text: Markdown text to render
        use_tabulate: Use tabulate for better table rendering (if available)

    Returns:
        Terminal-formatted text
    """
    global _TABULATE_STATUS_SHOWN

    # Show tabulate status only once when first table is encountered
    if use_tabulate and "|" in text and not _TABULATE_STATUS_SHOWN:
        if HAS_TABULATE:
            print("Using tabulate for enhanced table rendering")
        else:
            print("⚠️  You don't have tabulate installed. Install 'tabulate' package for better table rendering: pip install tabulate")
        _TABULATE_STATUS_SHOWN = True

    lines = text.split("\n")
    result = []
    i = 0

    while i < len(lines):
        line = lines[i]

        # Headers
        if line.startswith("# "):
            result.append(f"\033[1;36m{line[2:]}\033[0m")  # Bold cyan
        elif line.startswith("## "):
            result.append(f"\033[1;35m{line[3:]}\033[0m")  # Bold magenta
        elif line.startswith("### "):
            result.append(f"\033[1;33m{line[4:]}\033[0m")  # Bold yellow
        elif line.startswith("#### "):
            result.append(f"\033[1;32m{line[5:]}\033[0m")  # Bold green

        # Table detection and rendering
        elif "|" in line and line.strip() and use_tabulate and HAS_TABULATE:
            # Check if this looks like start of a table
            if line.strip().startswith("|") and line.strip().endswith("|"):
                # Try to parse table with tabulate
                table_data = parse_markdown_table("\n".join(lines[i:]))
                if table_data and len(table_data) >= 2:  # Need at least header + 1 row
                    headers = table_data[0]
                    rows = table_data[1:]
                    rendered_table = tabulate(rows, headers=headers, tablefmt="grid")
                    result.append(f"\033[37m{rendered_table}\033[0m")
                    # Skip all processed table lines
                    lines_to_skip = 0
                    for j in range(i, len(lines)):
                        if "|" in lines[j] and lines[j].strip():
                            lines_to_skip += 1
                        else:
                            break
                    i += lines_to_skip - 1  # -1 because i will be incremented at end of loop
                    continue
                # Fallback to simple rendering
                cells = [cell.strip() for cell in line.split("|")[1:-1]]
                formatted_cells = []
                for cell in cells:
                    if cell.replace("-", "").strip() == "":
                        formatted_cells.append(f"\033[90m{cell}\033[0m")
                    else:
                        formatted_cells.append(f"\033[37m{cell}\033[0m")
                result.append(f"│ {' │ '.join(formatted_cells)} │")
            else:
                result.append(line)

        # Simple table fallback (when tabulate not available)
        elif "|" in line and line.strip():
            if line.strip().startswith("|") and line.strip().endswith("|"):
                cells = [cell.strip() for cell in line.split("|")[1:-1]]
                formatted_cells = []
                for cell in cells:
                    if cell.replace("-", "").strip() == "":
                        formatted_cells.append(f"\033[90m{cell}\033[0m")
                    else:
                        formatted_cells.append(f"\033[37m{cell}\033[0m")
                result.append(f"│ {' │ '.join(formatted_cells)} │")
            else:
                result.append(line)

        # Bold **text**
        elif "**" in line:
            formatted = re.sub(r"\*\*(.*?)\*\*", r"\033[1m\1\033[0m", line)
            result.append(formatted)

        # Italic *text*
        elif "*" in line and not line.strip().startswith("*"):
            formatted = re.sub(r"\*(.*?)\*", r"\033[3m\1\033[0m", line)
            result.append(formatted)

        # Code blocks ```
        elif line.strip().startswith("```"):
            result.append(f"\033[90m{line}\033[0m")  # Dark gray

        # Inline code `code`
        elif "`" in line:
            formatted = re.sub(r"`(.*?)`", r"\033[100m\1\033[0m", line)  # Gray background
            result.append(formatted)

        else:
            result.append(line)

        i += 1

    return "\n".join(result)


# ========================================================================================================
# EXAMPLES
# ========================================================================================================


def run_all_examples_parallel(examples):
    """Run examples in parallel, print each as it completes in order"""
    import threading
    from time import sleep

    print("=== Unified OpenAI + Fireworks Prompt Examples (Parallel Execution) ===\n")

    results = [None] * len(examples)  # Pre-allocate list to store completion status for each example

    def run_example(i, desc, func):
        """Run example function and store result - shows silent item for example with debug=False"""
        result = func()
        # Check if this was a silent call by looking for debug=False in the description
        results[i] = f"Got result silently: '{result}'" if "Silent" in desc else ""

    # Start all threads
    threads = [threading.Thread(target=run_example, args=(i, desc, func)) for i, (desc, func) in enumerate(examples)]
    [t.start() for t in threads]

    # Print results in order as they complete
    printed = 0
    while printed < len(examples):
        if results[printed] is not None:
            print(f"\n{examples[printed][0]}")
            if results[printed]:  # Only print if not empty string
                print(results[printed])
            printed += 1
        else:
            sleep(0.1)

    [t.join() for t in threads]


if __name__ == "__main__":
    # Example models for each provider
    openai_model = "gpt-4.1-mini"
    fireworks_model = "accounts/fireworks/models/llama-v3p2-90b-vision-instruct"

    long_prompt = "This is a very long prompt that will normally be truncated at 150 characters but with debug_full=True it should show completely without any truncation at all, allowing you to see the entire input and output."

    examples = [
        ("=== Example 1: OpenAI Basic ===", lambda: prompt("What is 2+2?", provider="openai", max_tokens=50)),
        ("=== Example 2: Fireworks Basic ===", lambda: prompt("What is 2+2?", provider="fireworks", model=fireworks_model, max_tokens=50)),
        ("=== Example 3: OpenAI Creative ===", lambda: prompt("Write a haiku about coding", provider="openai", model=openai_model, max_tokens=100)),
        (
            "=== Example 4: Fireworks Creative ===",
            lambda: prompt("Write a haiku about coding", provider="fireworks", model=fireworks_model, max_tokens=100),
        ),
        (
            "=== Example 5: OpenAI Silent ===",
            lambda: prompt("What's the capital of Japan?", provider="openai", model=openai_model, debug=False, max_tokens=30),
        ),
        (
            "=== Example 6: Fireworks Silent ===",
            lambda: prompt("What's the capital of France?", provider="fireworks", model=fireworks_model, debug=False, max_tokens=30),
        ),
        ("=== Example 7: OpenAI Full Debug ===", lambda: prompt(long_prompt, provider="openai", model=openai_model, debug_full=True, max_tokens=100)),
        (
            "=== Example 8: Fireworks Full Debug ===",
            lambda: prompt(long_prompt, provider="fireworks", model=fireworks_model, debug_full=True, max_tokens=100),
        ),
        (
            "=== Example 9: OpenAI Markdown ===",
            lambda: prompt("Create a markdown table comparing Python vs JavaScript", provider="openai", render_markdown=True, max_tokens=200),
        ),
        (
            "=== Example 10: Fireworks Markdown ===",
            lambda: prompt(
                "Create a markdown table comparing cats vs dogs", provider="fireworks", model=fireworks_model, render_markdown=True, max_tokens=200
            ),
        ),
        ("=== Example 11: OAI Alias ===", lambda: oaiprompt("Quick test using oaiprompt alias", max_tokens=30)),
        ("=== Example 12: Fire Alias ===", lambda: fireprompt("Quick test using fireprompt alias", model=fireworks_model, max_tokens=30)),
    ]

    run_all_examples_parallel(examples)
