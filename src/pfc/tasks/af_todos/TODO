TODO AF: add these as issues / Project Tasks / Linear / Just make an XSLX here / Notebook here
TODO AF: models.py Scaffolding
TODO AF: Notebook Scaffolding for others to plug into -> add to main. Declare expectations re PRs.
TODO AF: Add 3-5 good end-to-end tet cases
- [ ] Integrate Borja Code?


Admin
- [ ] gitignore backups -> dir (not root)

Team Hygeine
- [ ] Add pre-commit hooks / CI (CC / Sourcery) for linting and testing. Check out the other options
- [ ] Share "Hide" Extension. Remove unecessary 