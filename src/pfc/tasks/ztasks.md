# zTasks.md - Visual Task Tracker

## Instructions
- This file tracks ALL Claude tasks and user requirements verbatim
- Task checklist at top in chronological order (oldest first, newest last)
- Complete tasks are checked off but NOT removed
- Rest of file organized by feature/branch for readability
- Sub-tasks are indented under their macro task
- Macro tasks hyperlink to the verbatim prompt that initiated them
- Each line item should indicate which agent created/completed it
- Agents should name themselves and include their ID for continuity

**Agent Format: [AgentName-ID] - e.g., [Claude-opus-4-20250514]**

---

## Task Checklist (Chronological Order)

- [x] Create .archive directory [<PERSON>-unknown]
- [x] Move all files and directories except /docs to .archive [<PERSON>-unknown]
- [x] Create root files: pyproject.toml, README.md [<PERSON>-unknown]
- [x] Create root directories: notebooks/, .tmp/, src/ [<PERSON>-unknown]
- [x] Create src/promptflow/ structure and __init__.py files [<PERSON>-unknown]
- [x] Create all subdirectories and their files [<PERSON>-unknown]
- [x] Create zTasks.md file [<PERSON>-unknown]
- [x] Test the created structure [<PERSON>-unknown]
- [x] Implement new directory structure from docs/dir_tree_2.md [<PERSON>-unknown]
- [x] Create all #CLAUDE.md documentation files [Claude-unknown]
- [x] Update pyproject.toml for new module structure [Claude-unknown]
- [x] Update justfile paths [Claude-unknown]
- [x] [Clean up pyproject.toml](#clean-pyproject) [Claude-opus-4-20250514]
  - [x] Remove Azure/enterprise-specific dependencies [Claude-opus-4-20250514]
  - [x] Add explanatory comments for all configuration [Claude-opus-4-20250514]
- [x] [Set up project with uv](#setup-uv) [Claude-opus-4-20250514]
  - [x] Copy pyproject.toml to root [Claude-opus-4-20250514]
  - [x] Run uv sync to install dependencies [Claude-opus-4-20250514]
  - [x] Run ruff to check code validity [Claude-opus-4-20250514]
  - [x] Configure ruff to exclude .archive directory [Claude-opus-4-20250514]
  - [x] Verify project setup with uv pip check [Claude-opus-4-20250514]
- [x] [Move zTasks to src/pfc/tasks](#move-ztasks) [Claude-opus-4-20250514]
  - [x] Create src/pfc/tasks directory [Claude-opus-4-20250514]
  - [x] Move zTasks.md content to src/pfc/tasks/zTasks.md [Claude-opus-4-20250514]
  - [x] Update root zTasks.md with redirect notice [Claude-opus-4-20250514]
- [x] [Update zTasks.md format and tracking](#update-ztasks-format) [Claude-opus-4-20250514]
  - [x] Add comprehensive tracking instructions [Claude-opus-4-20250514]
  - [x] Include all verbatim user requirements [Claude-opus-4-20250514]
  - [x] Add agent identification to all tasks [Claude-opus-4-20250514]
- [x] [Add local CLAUDE.md rules](#add-claude-rules) [Claude-opus-4-20250514]
  - [x] Create local CLAUDE.md with project-specific rules [Claude-opus-4-20250514]
- [x] [Add uv sync to justfile](#add-uv-justfile) [Claude-opus-4-20250514]
  - [x] Add uv sync commands to justfile setup section [Claude-opus-4-20250514]
- [x] [Add show commands to justfile and update mermaid diagram](#update-justfile-mermaid) [Claude-opus-4-20250514]
  - [x] Add 'just show-mermaid' command linking to docs/FlowChart Mermaid Chart July 9 2025.mmd [Claude-opus-4-20250514]
  - [x] Add 'just show-architecture' command linking to docs/architecture_rules_1_9jul.md [Claude-opus-4-20250514]
  - [x] Update mermaid diagram with new directory names (evaluate_prompt, optimize_from_outputs, realign_from_requirements) [Claude-opus-4-20250514]
  - [x] Commit changes with message "feat: update justfile with show commands and mermaid diagram with new directory names -Claude" [Claude-opus-4-20250514]
- [x] [Configure ruff linting rules for brittle code philosophy](#configure-brittle-linting) [Claude-opus-4-20250514]
  - [x] Add ruff ignore rules for brittle code philosophy (S101, BLE001, TRY203, T201, E402) [Claude-opus-4-20250514]
  - [x] Commit changes with explanation [Claude-opus-4-20250514]
- [x] [Add complexity limits for maintainable code](#add-complexity-limits) [Claude-opus-4-20250514]
  - [x] Configure max Pydantic model LOC length using PLR rules [Claude-opus-4-20250514]
  - [x] Add per-file ignores for notebooks, scripts, and archived code [Claude-opus-4-20250514]
  - [x] Add McCabe complexity limit (C901) for nested if depth [Claude-opus-4-20250514]
  - [x] Create auto_linting_TODO.py script placeholder [Claude-opus-4-20250514]
  - [x] Test all linting rules with test_linting.py [Claude-opus-4-20250514]
- [x] [Update scenario example and refactor versioning](#update-scenario-versioning) [Claude-opus-4-20250514]
  - [x] Update scenario_doc_example.json to match Scenario class structure [Claude-opus-4-20250514]
  - [x] Analyze versioning method duplication in models.py [Claude-opus-4-20250514]
  - [x] Create version.py utility module with bump_version function [Claude-opus-4-20250514]
  - [x] Refactor models to use shared bump_version utility [Claude-opus-4-20250514]
  - [x] Fix ONER_MAX_LENGTH ClassVar annotation [Claude-opus-4-20250514]
- [x] [Fix ztasks.md format](#fix-ztasks-format) [Claude-opus-4-20250514]
  - [x] Review current format and identify missing tasks [Claude-opus-4-20250514]
  - [x] Add all missing tasks from today's conversation [Claude-opus-4-20250514]
  - [x] Add all missing verbatim user requirements [Claude-opus-4-20250514]
  - [x] Ensure chronological order is maintained [Claude-opus-4-20250514]
- [x] [Commit refactoring changes](#commit-refactoring) [Claude-opus-4-20250514]
  - [x] Commit version utility refactoring and ztasks update [Claude-opus-4-20250514]
- [x] [Check Sourcery installation](#check-sourcery) [Claude-opus-4-20250514]
  - [x] Check if Sourcery CLI is installed [Claude-opus-4-20250514]
  - [x] Determine Sourcery usage approach [Claude-opus-4-20250514]
- [x] [Add Sourcery documentation](#add-sourcery-docs) [Claude-opus-4-20250514]
  - [x] Create docs/libs/documentation/sourcery directory [Claude-opus-4-20250514]
  - [x] Fetch VS Code Getting Started guide [Claude-opus-4-20250514]
  - [x] Fetch configuration documentation and .sourcery.yaml guide [Claude-opus-4-20250514]
  - [x] Fetch CLI usage documentation [Claude-opus-4-20250514]
  - [x] Fetch rules configuration guide [Claude-opus-4-20250514]
  - [x] Update zTasks with new tasks [Claude-opus-4-20250514]
- [x] [Convert bash commands to Python in auto_linting_TODO.py](#convert-bash-to-python) [Claude-opus-4-20250514]
  - [x] Convert bash commands to Python functions while keeping comments [Claude-opus-4-20250514]
  - [x] Add functions to main() [Claude-opus-4-20250514]
  - [x] Add rich report at end of file [Claude-opus-4-20250514]
- [x] [Reorganize dev dependencies in pyproject.toml](#reorganize-dev-deps) [Claude-opus-4-20250514]
  - [x] Add dev dependencies using uv [Claude-opus-4-20250514]
  - [x] Move existing dev deps from main deps to dev deps section [Claude-opus-4-20250514]
- [x] [Add auto-lint command to justfile](#add-auto-lint-justfile) [Claude-opus-4-20250514]
  - [x] Add command to run main() from auto_linting_TODO.py [Claude-opus-4-20250514]
  - [x] Commit all changes [Claude-opus-4-20250514]
  - [x] Update zTasks [Claude-opus-4-20250514]
- [x] [Create DAG dependency visualization](#create-dag-viz) [Claude-opus-4-20250514]
  - [x] Read through pipeline documentation and mermaid chart [Claude-opus-4-20250514]
  - [x] Analyze src/ directory structure [Claude-opus-4-20250514]
  - [x] Create DAG.md showing parallelization opportunities and blocking points [Claude-opus-4-20250514]
  - [x] Create functional composition view of pipeline [Claude-opus-4-20250514]
  - [x] Update DAG with corrected dependencies and formatting [Claude-opus-4-20250514]
  - [x] Create ScenarioDoc JSON example with 15 test scenarios [Claude-opus-4-20250514]
  - [x] Create Mermaid diagram and ASCII art visualizations [Claude-opus-4-20250514]
- [x] [Review and split architecture rules document](#split-architecture-rules) [Claude-opus-4-20250514]
  - [x] Review architecture_rules_2_pydantic_extra_11jul.md [Claude-opus-4-20250514]
  - [x] Extract key decisions from O3 chat [Claude-opus-4-20250514]
  - [x] Create architecture_rules_2_pydantic_extra_11jul_naming_o3_chat.md with full transcript [Claude-opus-4-20250514]
  - [x] Update main document with essential guidelines only [Claude-opus-4-20250514]
- [x] [Configure editor settings for markdown and Python](#configure-editor-settings) [Claude-opus-4-20250514]
  - [x] Enable markdown autocomplete in global Cursor settings [Claude-opus-4-20250514]
  - [x] Add hover tooltips for problem diagnostics [Claude-opus-4-20250514]
  - [x] Configure Python folding settings and keybindings [Claude-opus-4-20250514]
  - [x] Add Explicit Folding extension config for auto-folding docstrings [Claude-opus-4-20250514]
  - [x] Add Sourcery YAML schema for autocomplete [Claude-opus-4-20250514]

---

## Verbatim User Requirements

### <a id="clean-pyproject">Clean up pyproject.toml</a>
**User:** "read @docs/dir_setup_DM_pyproject.toml -> this was written for a project involving MS azure. Remove anything relating to that or Apollo or Enterprise etc that we won't need, add EOL comments (if allowed) to explain everything remaining. Keep most deps unless Azure/enterprise related."

### <a id="setup-uv">Set up project with uv</a>
**User:** "Copy to root, uv sync then run ruff / uv to check validity."

### <a id="move-ztasks">Move zTasks to src/pfc/tasks</a>
**User:** "move ztasks to src/p/tasks. Leave zTasks.md in root but it should just have a note saying to not update it, to instead update [path]"

### <a id="update-ztasks-format">Update zTasks.md format and tracking</a>
**User:** "Please put ALL claude tasks, as well as each of my stated requirements, verbatim copies of what I say to you (but not verbatim replies) in zTasks.md as I'll have it open as we go on the side. It's a visual tracker for me. The task checklist should be at the top and the order is top = first, bottom = last. New tasks go on the bottom of that checklist. The rest of the file should be organised by feature / branch / whatever to keep it readable, but the task list should be chronological. Check and cross-off complete tasks but don't remove them. Should look identical to your own task list except for not removing sub-tasks between majortasks. You should indent sub-tasks for clear hierarchy and include the macro task they're attached to. The macro task should be markdown hyperlinked to the verbatim prompt I gave to initiate that macro task. -> put this at the top of that file then update with your tasks. Add a note to the top instruction re declaring which agent made which line items / completed them. Agents should name themselves for convenience but also put their ID (for claude code continuing)"

### <a id="add-claude-rules">Add local CLAUDE.md rules</a>
**User:** "OK add this to claude local rules: PURPOSEFUL BRITTLENESS DURING LLM DEV

- dont use defaults
- don't do exception handling
- don't mock apis
- don't add fallbacks
- etc
- this is important

VIRTUAL ENVIRONMENT CONVENTIONS

- use .venv instead of venv for virtual environments

SECURITY AND CONFIGURATION

- always put keys etc in .env file and access via dotenv
- config params should be stored globally either in CFG singleton in run.py or run_config.py

DEVELOPMENT WORKFLOW

- commit regularly during operations for easy save points `<feat>` `<fix>` tags etc. use sub-tasks (parllel) for this to prevent blocking the main claude code thread.
- always update claude.md using subtasks to not block the main claude code thread.
- always run scripts and existing tests to prevent regression errors.
- after completing tasks, suggest short list of regression tests to add to ./tests to prevent regression errors going forward. Each suggestion should be a single line "if [function] does/doesn't x then broken" for readability ease. When then writing those tests, those single line descriptions should be added to that tests success/fail print.
- Always run scripts after editing to check they work

REPO DOCUMENTATION

- maintain a comprehensive spec section that serves as a complete instruction set for rebuilding the repo from scratch
- update claude.md incrementally to track design choices and repository structure
- ensure the spec can be used as a standalone guide for repository reconstruction

FILE NAMING CONVENTIONS

- if file isn't working it should have **_WIP.** suffix, changed back once confirmed working and tests working.

PROJECT STRUCTURE AND SCRIPT CONVENTIONS

- use ./src/appname and ./tests, ./.venv, ./.claude, ./CLAUDE.md, ./scripts and ./run.py file structure
- launch scripts should be named run_***.sh
- include a justfile in root to provide a minimalistic cheatsheet for running scripts, main app, tests, setup etc. for other developers

CODE REFERENCE AND RESEARCH

- Always check mcp context7 for code snippets for APIs if available (use minimal keywords, try several things in parallel as it filters quite aggressively)
- Always check online for working code snippets to avoid hallucinations

RUN SCRIPT CONVENTIONS

- create run.sh with a standard template that includes shebang, error handling, and executable permissions
- ensure run.sh can be used to launch the main application, run tests, or perform setup tasks

MODULE INITIALIZATION

- add blank __init__.py to all app dirs, /scripts etc to make them modules. If using uv for deps, register modules in pyproject.toml.

TASK MANAGEMENT AND DEVELOPMENT

- Always explicitly plan what you can do as sub-task agents to maximise speed and parellisation of your actions.

IMPORT AND MODULE MANAGEMENT

- never use the os.path hack to fix import issues. do it properly, check pyproject.toml or module structure.

CODE CLARITY AND READABILITY

- Explicitly State elif when multiple states, don't leave reader guessing. if x : abc  elif y: xyz. Not just else xyz.

GIT COMMIT CONVENTIONS

- please sign git commit messages -Claude for clarity

JUSTFILE DOCUMENTATION

- Provide a concise, one-line description of the justfile's purpose and ideal formatting to serve as a quick reference for developers

TABLE FORMATTING

- always include rich table divider lines

TASK TRACKING

- Maintain a zTasks.md file as a visual tracker for tasks
- Task checklist should be at the top of zTasks.md, in chronological order
- New tasks go at the bottom of the checklist
- Complete tasks should be checked off but not removed
- Include verbatim copies of tasks and requirements
- Organize the rest of the file by feature/branch for readability
- Indent sub-tasks to show hierarchy
- Macro tasks should be markdown hyperlinked to the original prompt
- zTasks.md should be in the root directory and gitignored

TASK LIST MANAGEMENT

- Don't mock anything not explicitly told to mock
- Maintain a list of tasks at the end of the task list
- Prepend personal tasks with af_** prefix
- If a task has a dependency on the user, move it after the relevant "af_" task
- Pause and wait for user input if encountering a task with user dependency

PERSONAL DETAILS

- 'af' is my name. Alex Foster.

DEBUGGING AND TESTING

- how do you know it's working? add minimal test cases that print or log core functionality to verify basic system behavior
- ALWAYS RUN AND TEST YOUR CODE BEFORE COMPLETING. how else do you know it works?

CLI TOOLS

- cc = claudecode

PARSING AND DATA EXTRACTION

- I very rarely use programatic parsers. Almost always we will use LLMs w structured outputs for this."

### <a id="add-uv-justfile">Add uv sync to justfile</a>
**User:** "add uv sync commands to just file as first item under setup/install."

### <a id="update-justfile-mermaid">Add show commands to justfile and update mermaid diagram</a>
**User:** "add 'just show mermaid' and just show architecturerules to justfile, linking to docs/architecture_rules_1_9jul.md and docs/FlowChart Mermaid Chart July 9 2025.mmd"
**User:** "better name for 'optimize' which captures 'continuous re-alignment' but starts with letter after refine and before ship. ideas?"
**User:** "see what I renamed the three dirs to."
**User:** "also evaluate prompt and optimize from output."
**User:** "Let's update those in the mermaid diagram in /docs."

### <a id="configure-brittle-linting">Configure ruff linting rules for brittle code philosophy</a>
**User:** "update pyproject.toml for ruff to ignore these, include the comments as well # Given your brittle code philosophy
ignore = [
    "S101",    # Assert statements are fine for brittle code
    "BLE001",  # Blind exceptions - you want to fix these manually
    "TRY203",  # Re-raising exceptions
    "T201",    # Print statements (if using rich prints)
    "E402",    # Import order in notebooks
] then commit and explain why added"

### <a id="add-complexity-limits">Add complexity limits for maintainable code</a>
**User:** "can we set a lint for the max pydantic model LOC length? check it works."
**User:** "what's a branch max? concise answer"
**User:** "add a script in /scripts called auto_linting_TODO.py"
**User:** "can we limit if statement depth using a standard ruff code?"
**User:** "Are there automatic measures of Coupling, DRY (outside of main() functions), code complexity, etc and can we somehow automate some of the sourcery refactorings? Some of them are excellent. answer don't do it."
**User:** "check your previous lints worked with a test_linting file in /tests."
**User:** "update ztasks in root. are these all in pyproject?"

### <a id="update-scenario-versioning">Update scenario example and refactor versioning</a>
**User:** "see what I renamed the three dirs to."
**User:** "Then commit and sync, update ztasks"
**User:** "better name for 'optimize' which captures 'continuous re-alignment' but starts with letter after refine and before ship. ideas?"
**User:** "also evaluate prompt and optimize from output."
**User:** "Let's update those in the mermaid diagram in /docs."
**User:** "check @docs/architecture_rules_1_9jul.md re pydantic - add notes into docstring of models.py"
**User:** "OK, can we squash a lot of these commits together to tidy them up? How best to do that? Then turn on main-protection."
**User:** "This is too much, just disable direct commits to main, anyone can merge. I can always undo it later."
**User:** "we'll come back to it. Check out @src/pfc/core/model.py_examples/ScenarioDoc/example1/scenario_doc_example.json -> update in-line with Scenario class in @src/pfc/core/models.py"
**User:** "then commit with 'added thorough list of potential test cases, OTT but can easily comment out / add. Generator can specify which to use.'"
**User:** "see versioning method in @models.py -> should this be abstracted? Read rules at top of file and in @docs/architecture_rules_1_9jul.md"
**User:** "Great, do it, put at top or in /utils"
**User:** "update ztasks in root."

### <a id="fix-ztasks-format">Fix ztasks.md format</a>
**User:** "are you following the format? Please put ALL claude tasks, as well as each of my stated requirements, verbatim copies of what I say to you (but not verbatim replies) in zTasks.md as I'll have it open as we go on the side. It's a visual tracker for me. The task checklist should be at the top and the order is top = first, bottom = last. New tasks go on the bottom of that checklist. The rest of the file should be organised by feature / branch / whatever to keep it readable, but the task list should be chronological. Check and cross-off complete tasks but don't remove them. Should look identical to your own task list except for not removing sub-tasks between majortasks. You should indent sub-tasks for clear hierarchy and include the macro task they're attached to. The macro task should be markdown hyperlinked to the verbatim prompt I gave to initiate that macro task."

### <a id="commit-refactoring">Commit refactoring changes</a>
**User:** "commit these changes."

### <a id="check-sourcery">Check Sourcery installation</a>
**User:** "is the sourcery cli installed? can we use it for refactoring?"

### <a id="add-sourcery-docs">Add Sourcery documentation</a>
**User:** "We can use it for automated 'linting' -type refactorings though and to constrict LLM agent's work. I'll install it now"
**User:** "https://docs.sourcery.ai/Coding-Assistant/Guides/Getting-Started/VSCode/ -> add all relevant docs to docs/libs/documentation/sourcery Please parellise simple tasks to achieve your task as fast as posisble. 
Update zTasks."

### <a id="convert-bash-to-python">Convert bash commands to Python in auto_linting_TODO.py</a>
**User:** "@scripts/auto_linting_TODO.py -> there's some bash in here, can we keep the comments but convert those to python functions and add to main()? careful!"
**User:** "Add a rich report to end of that file so we can see the outputs."

### <a id="reorganize-dev-deps">Reorganize dev dependencies in pyproject.toml</a>
**User:** "uv add them to dev deps as well, and move any dev deps in main deps in pyproject over to dev deps as well plz."

### <a id="add-auto-lint-justfile">Add auto-lint command to justfile</a>
**User:** "add running main() from it to justfile then commit these changes and update ztasks"

### <a id="create-dag-viz">Create DAG dependency visualization</a>
**User:** "read through @af1 and docs and pipeline dir (src/). Draw out a DAG dependendency list showing very clearly what can be parellised / is blocking, what is fan-out and what is fan-in. Use syntax to keep this concise and # EOL comments for explanation."
**User:** "put whole output into md file called dag in .tmp/af2"
**User:** "[Input] → {d1_PromptDoc, d2_ScenarioDoc, d3_ReqDoc}  # Initial doc generation - FAN-OUT (1->3) These are actually in series, with ScenarioDoc last."
**User:** "Put each scriptname in [] to make them pop. Groups should be in <>"
**User:** "can we mimick he alphabetical order of the src dir as best we can for easier reading."
**User:** "Add A second section (---) divider that uses scriptname([scriptname],[scriptname])->[scriptname] as an alternative documentation format."
**User:** "Replace 'Input' object with ProblemDescription as in the mermaid."
**User:** "I've moved some files and dirs around, print the tree then describe changes and how we MIGHT change the DAG.md"
**User:** "do it, don't lose my formatting upgrades."
**User:** "For the underlying Dict of the ScenarioDoc, should be something like this: If the Scenario doc's underlying JSON is something like {groupa: {1: { if_this: words then_that: words }, 2: etc Can you extend this into an example? Say 15 scenarios."
**User:** "add src/pfc/core/model.py_examples/modelname/example1/2/3 etc and add this as the example for ScenarioDoc. Then add a filepath to a comment just below the model in models.py"

### <a id="split-architecture-rules">Review and split architecture rules document</a>
**User:** "I like TestSpec. OK, Review @docs/architecture_rules_2_pydantic_extra_11jul.md -> What do you think we SHOULD take from this and why?"
**User:** "OK great -> See @docs/architecture_rules_2_pydantic_extra_11jul.md -> I added what you just said as It's clear and concise, but some of the other points from o3 are also clear. The one's we're keeping, let's keep (eg chesterton's fence point). Remove the o3 section and just keep what you think are great points / rules to carry forward."
**User:** "Update the doc. Move the O3 chat to new doc with same name and location but _naming_o3_chat.md then just include the best bits / most explanatory like the chesterton fence points..."

### <a id="configure-editor-settings">Configure editor settings for markdown and Python</a>
**User:** "I don't think Cursor is autocompleting in markdown docs, can you check settings?"
**User:** "did you do this locally or globally?"
**User:** "update global settings, undo local settings change."
**User:** "[Image #1] can vscode/cursor have this problem pane appear if leaving cursor (moved via keyboard) on a problem (underlined)?"
**User:** "can we auto-fold document and function dostrings in settings? check docs."
**User:** "can we auto-fold docstrings?"
**User:** "Can we add Sourcery YAML autocomplete via this plz These instructions assume you have the Red Hat YAML extension for VSCode installed."
**User:** "installed explicit folding. Where are the settings you made for it? Concise answer. update ztasks"

---

## Feature/Branch Organization

### Initial Setup
- Created directory structure as specified in docs/dir_tree_1.md
- Archived existing codebase to .archive/
- Set up new project structure for promptflow

### Directory Structure Implementation (v1)
- Root level: pyproject.toml, README.md, notebooks/, .tmp/, src/
- Main module: src/promptflow/
- Layer modules: aa_input_layer, bb_core_generation, cc_testing_evaluation, dd_refinement, ee_optimisation, ff_final_delivery
- Support modules: utils/, tasks/
- All __init__.py files created for proper module structure

### Directory Structure Implementation (v2)
- Implemented "Duplo-alphabetised" structure from docs/dir_tree_2.md
- New package root: src/p/ (short prefix for runtime package)
- Cross-cutting core: aa_core/ with models, prompts, pipeline, utils
- Stage groups: bb_core_generation, cc_testing_evaluation, dd_refinement, ee_optimisation, ff_final_delivery
- Iteration machines: bd_iteration_machine_1, dc_iteration_machine_2, ec_iteration_machine_3
- Every package includes #CLAUDE.md documentation
- Updated pyproject.toml with new module structure and dependencies
- Moved compile script to src/p/aa_core/utils/scripts/

### Project Configuration and Setup
- Cleaned pyproject.toml removing Azure/enterprise dependencies
- Added explanatory comments to all configuration options
- Set up project with uv package manager
- Configured ruff linter with proper exclusions
- Verified all dependencies and imports working correctly
- Moved zTasks.md to proper location with redirect notice

### Linting and Code Quality Configuration
- Configured ruff to ignore rules for brittle code philosophy (S101, BLE001, TRY203, T201, E402)
- Added complexity limits: max 25 lines for Pydantic models (PLR0915)
- Set McCabe complexity limit (C901) to prevent deeply nested code
- Added per-file ignores for notebooks, scripts, and archived code
- Created test_linting.py to verify all rules work correctly
- Created auto_linting_TODO.py placeholder for future automation

### Documentation and Architecture Updates
- Added comprehensive Pydantic architecture rules to models.py docstring
- Updated justfile with show-mermaid and show-architecture commands
- Updated mermaid diagram with renamed directories:
  - evaluate_prompt (formerly refine)
  - optimize_from_outputs (formerly optimize)  
  - realign_from_requirements (new stage)
- Configured branch protection (attempted, deferred for simpler setup)

### Model Refactoring and Examples
- Updated scenario_doc_example.json to match current Scenario class structure
- Refactored version bumping logic:
  - Created utils/version.py with shared bump_version function
  - Updated PromptDoc, ReqDoc, ScenarioDoc to use shared utility
  - Fixed ONER_MAX_LENGTH to use ClassVar annotation
- Maintained architecture principle: non-critical transforms in helpers

### DAG Visualization and Pipeline Documentation
- Created comprehensive DAG dependency visualization in .tmp/af2/dag.md
- Documented parallelization opportunities and blocking points
- Created functional composition view showing pipeline data flow
- Updated with correct script names and directory structure
- Created ASCII art and Mermaid diagram visualizations of pipeline
- Added ScenarioDoc JSON example with 15 comprehensive test scenarios
- Resolved naming ambiguity: TestSpec → TestRun → TestResult → TestGrade

### Architecture Rules Refactoring
- Reviewed architecture_rules_2_pydantic_extra_11jul.md for industry best practices
- Extracted key decisions: naming conventions, immutability patterns, Chesterton's fence principles
- Split document: moved O3 chat transcript to separate file
- Kept essential guidelines: transformation chain, iteration machines, frozen objects benefits