# Development tasks for promptflow project
# Run 'just' to see available commands

# Default - show available commands
default:
    @just --list


#------------
# SETUP
#------------

# Install dependencies with uv, dev deps include pre-commit, ruff, ty, pytest, pytest-asyncio, pytest-timeout, pytest-dotenv, pytest-mock, pytest-cov
# This will also compile to requirements.txt if preferred not to use uv.
# uv add xxx is how you add dependencies.
setup:
    uv sync
setup-dev:
    uv sync --dev
    @bash src/pfc/core/utils/scripts/compile_requirements.sh
    # if [ ! -d ".venv" ]; then python -m venv .venv; fi
    # source .venv/bin/activate
    # pip install --upgrade pip
    pip install -r requirements.txt

# Compile requirements.txt from pyproject.toml
compile-deps:
    @bash src/pfc/core/utils/scripts/compile_requirements.sh

#------------
# RUN
#------------
# run project with cli commands for custom input / config.
# run: #TODO
# run project end-to-end using root run.py/sh and demo content.
# run-demo:#TODO
# run-ui-demo:#TODO
# run package 1#TODO
# run package 2 #TODO
# run package 3 #TODO
# etc. TODO: 



#------------
# DIAGNOSTICS
#------------

# Run tests
test:
    uv run pytest tests/ || echo "No tests directory found"

# Auto Format code with linters (just ruff atm)
fix:
    @echo "This will auto-lint your whole src folder using ruff, continue? \n y/n or 'full' for unsafe fixes:"
    @read -r response && [ "$$response" = "y" ] || (echo "Aborted." && exit 1)
    uv run ruff format --output-format=concise src/ 
    uv run ruff check --fix src/


# Lint code - just check for errors
lint:
    uv run ruff check --output-format=concise src/

# Type check
typecheck:
    uv run ty src/

# Auto-linting analysis (brittleness metrics, complexity, etc.)
auto-lint:
    @echo "Running comprehensive code analysis..."
    uv run python scripts/auto_linting_TODO.py

# Clean temporary files
clean:
    find . -type d -name "__pycache__" -exec rm -rf {} +
    find . -type f -name "*.pyc" -delete
    rm -rf .tmp/*

#------------
# SHOW / DOCUMENTATION ETC
#------------

# Show various critical docs/diagrams
show-mermaid:                   # core diagram
    cat "docs/FlowChart Mermaid Chart July 9 2025.mmd"
show-architecture:              # core architecture rules.
    cat "docs/architecture_rules_1_9jul.md"


#------------
# SOURCERY - AF auto-linting/refactoring experiments. Feel free to ignore.
#------------

# Sourcery refactoring - show diffs without applying
sourcery-diff:
    sourcery refactor src/ --diff

# Sourcery refactoring - apply changes in-place
sourcery-apply:
    sourcery refactor src/ --in-place

# Sourcery review - detect issues (requires login)
sourcery-review:
    sourcery review src/ --enable default --enable google-python-style-guide

# Sourcery review - fix issues automatically
sourcery-fix:
    sourcery review src/ --fix --enable use-fstring-for-concatenation --enable use-named-expression

# Sourcery on changed files only (git diff)
sourcery-changes:
    git diff --name-only | grep '\.py$' | xargs -r sourcery refactor --diff

# Sourcery metrics check (exit 1 if issues found) - for CI
sourcery-ci:
    sourcery refactor src/ --check