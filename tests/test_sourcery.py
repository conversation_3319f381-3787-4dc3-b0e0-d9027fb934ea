"""Test file for Sourcery CI."""


def concatenate_strings(name, greeting):
    # This should trigger use-fstring suggestion
    message = greeting + " " + name + "!"
    return message


def check_type(obj):
    # This should trigger use-isinstance suggestion
    if type(obj) == list:
        return True
    return False


def build_list():
    # This should trigger list-comprehension suggestion
    result = []
    for i in range(10):
        result.append(i * 2)
    return result


# To reset the test, replace the above with the below
"""

def concatenate_strings(name, greeting):
    # This should trigger use-fstring suggestion
    message = greeting + " " + name + "!"
    return message

def check_type(obj):
    # This should trigger use-isinstance suggestion
    if type(obj) == list:
        return True
    return False

def build_list():
    # This should trigger list-comprehension suggestion
    result = []
    for i in range(10):
        result.append(i * 2)
    return result
"""
