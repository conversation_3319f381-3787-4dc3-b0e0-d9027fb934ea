{
    "recommendations": [
        // AI and Coding Assistants
        "anthropic.claude-code",  // required
        // "github.copilot",
        // "github.copilot-chat",
        "openai.chatgpt",          // Chatgpt Desktop can use open files for context.
        "augment.vscode-augment",  // One of the best aternative ageents: specialisation in iterative development & context mgmt.
        // "rooveterinaryinc.roo-cline",  // <PERSON><PERSON> and Roo often have experimental features  that are inspiring.
        
        // Python Development
        "ms-python.python",         // Required
        // "ms-python.vscode-pylance",
        "ms-python.debugpy",        
        "charliermarsh.ruff",        // Required
        "ms-python.black-formatter", // Required
        "ms-python.isort",           // Required
        // "pydantic.logfire",          // Will potentially use as tracer if OAI tracer not turned on soon.
        
        // Jupyter and Data Science
        "ms-toolsai.jupyter",            // Required
        // "ms-toolsai.jupyter-keymap",  // QoL
        "ms-toolsai.jupyter-renderers",  // Required
        // "ms-toolsai.vscode-jupyter-cell-tags",
        // "ms-toolsai.vscode-jupyter-slideshow",
        
        // File Viewers and Editors
        "cweijan.vscode-office",        // WYSIWYG for markdown and word/excel files. Renders mermaid, codeblocks etc.
        "dvirtz.parquet-viewer",        // Required. Parquet is standard blob (xlsx /json replacement)for data science.
        "grapecity.gc-excelviewer",     // Required
        // "mechatroner.rainbow-csv",      
        "tomoki1207.pdf",
        "telesoho.vscode-markdown-paste-image",
        "zaaack.markdown-editor",
        
        // JSON/YAML/TOML
        "blueglassblock.better-json5",
        "hilleer.yaml-plus-json",
        "imgildev.vscode-json-flow",
        "redhat.vscode-yaml",
        "tamasfe.even-better-toml",
        "zardoy.fix-all-json",
        
        // Themes and Icons
        // "catppuccin.catppuccin-vsc",
        // "catppuccin.catppuccin-vsc-icons",
        // "alexdauenhauer.catppuccin-noctis",
        // "dooez.alt-catppuccin-vsc",
        // "thang-nm.catppuccin-perfect-icons",
        // "azemoh.one-monokai",
        // "monokai.theme-monokai-pro-vscode",
        
        // Development Tools
        "arjun.swagger-viewer",
        "formulahendry.code-runner",
        "ms-azuretools.vscode-containers",
        "ms-vscode-remote.remote-containers",
        
        // Platform Specific
        "idleberg.applescript",
        "bierner.markdown-mermaid"  // renders markdown in cursor notebooks
    ]
}