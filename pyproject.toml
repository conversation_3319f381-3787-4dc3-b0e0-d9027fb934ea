[project]
name = "pfc"  # Replace with your project name
version = "0.0.1"  # Semantic versioning
description = "Your project description"  # Brief project description
readme = "README.md"  # Points to project readme file
authors = [{ name = "<PERSON>", email = "<EMAIL>" }]  # Project maintainers
requires-python = ">=3.12"  # Python version requirement
dependencies = [
    "fastapi>=0.115.12", # Web framework for building APIs
    "uvicorn>=0.34.0", # ASGI server for FastAPI
    "mcp[cli]>=1.6.0", # Model Context Protocol CLI tools
    "pydantic>=2.11.7", # Data validation using Python type annotations
    "aiofiles>=24.1.0", # Async file operations
    # Agentic
    "openai-agents>=0.0.14", # OpenAI agents framework
    "openai>=1.0.0", # OpenAI API client
    # NLP
    "rapidfuzz>=3.0.0", # Fast string matching library
    # HTTP and utilities
    "requests>=2.31.0", # HTTP library
    "python-dotenv>=1.0.0", # Environment variable management
    "tabulate>=0.9.0", # Table formatting
    "pandas>=2.3.1",
]

# Local development dependencies - uncomment to use local packages
# [tool.uv.sources]
# package-name = { path = "../local-package-path", editable = true }


[dependency-groups]
dev = [
    "pre-commit", # Git hook scripts for code quality
    "ruff", # Fast Python linter and formatter
    "ty>=0.0.1a13", # Type checker for Python
    "sourcery-cli>=1.20.0", # Code quality and refactoring tool
    # Test
    "pytest", # Testing framework
    "pytest-asyncio", # Pytest support for asyncio
    "pytest-timeout", # Timeout plugin for pytest
    "pytest-dotenv", # Load .env files in pytest
    "pytest-mock", # Thin wrapper around mock for pytest
    "pytest-cov", # Coverage plugin for pytest
    "pip-tools>=7.4.1",
    # Code Analysis
    "radon>=5.1.0", # Code complexity measurement
    "xenon>=0.9.3", # Code complexity enforcement
    "pylint>=3.3.7", # Python code analysis
    "vulture>=2.14", # Dead code detection
    "wily>=1.25.0", # Code complexity tracking over time
    "rich>=13.7.0", # Rich terminal formatting
]

# Configure custom package indexes if needed
# [[tool.uv.index]]
# name = "private-pypi"
# url = "https://your-private-pypi.com/simple/"
# default = true

[build-system]
requires = ["hatchling"]  # Build dependencies
build-backend = "hatchling.build"  # PEP 517 build backend

[tool.hatch.build.targets.wheel]
packages = ["src/pfc"]  # Package directory to include in wheel

# -------------------------
# LINTING - AF: EXTENSIVE FOR CODING AGENT CONTROL & CONVERGENCE.
# -------------------------


[tool.ruff]
line-length = 150  # Maximum line length (150 is AF preference.)
target-version = "py312"  # Match requires-python version
indent-width = 4  # Number of spaces per indentation level
extend-exclude = [
    # Existing exclusions
    ".archive",      # Archive directory
    ".venv",         # Virtual environment
    "build",         # Build directories
    "dist",          # Distribution files
    "*.egg-info",    # Egg info directories
    "__pycache__",   # Python cache
    ".ruff_cache",   # Ruff cache
    ".pytest_cache", # Pytest cache
    "notebooks",     # Jupyter notebooks (have many formatting issues)
    ".tmp",          # Temporary files directory
    "docs",          # Documentation directory
    
    # Additional exclusions
    ".mypy_cache",   # Mypy type checker cache
    ".sourcery",     # Sourcery analysis cache
    ".cursor",       # Cursor IDE configuration
    ".vscode",       # VS Code configuration
    ".idea",         # JetBrains IDE configuration
    ".coverage",     # Coverage report files
    ".coverage.*",   # Coverage data files
    "htmlcov",       # HTML coverage reports
    "*.pyc",         # Python compiled files
    "*.pyo",         # Python optimized files
    "*.pyd",         # Python extension modules
    "*.so",          # Shared object files
    "*.dll",         # Dynamic link libraries
    "*.log",         # Log files
    "*.lock",        # Lock files (uv.lock, etc.)
    "justfile",      # Just command runner
    "Makefile",      # Make files
    "*.mk",          # Make include files
    ".env",          # Environment files
    ".env.*",        # Environment variant files
    "*.orig",        # Merge conflict originals
    "*.rej",         # Patch rejection files
    "*.bak",         # Backup files
    "*.swp",         # Vim swap files
    "*.swo",         # Vim swap overflow
    "*~",            # Editor backup files
    ".DS_Store",     # macOS metadata
    "Thumbs.db",     # Windows thumbnail cache
    ".hypothesis",   # Hypothesis testing cache
    ".tox",          # Tox testing environments
    ".nox",          # Nox testing environments
    "site",          # MkDocs build output
    "_build",        # Sphinx build output
    "node_modules",  # Node.js dependencies (if any)
]

[tool.flake8]
max-line-length = 150

[tool.black]
line-length = 150

[tool.isort]
line_length = 150
extend-exclude = [
    # Existing exclusions
    ".archive",      # Archive directory
    ".venv",         # Virtual environment
    "build",         # Build directories
    "dist",          # Distribution files
    "*.egg-info",    # Egg info directories
    "__pycache__",   # Python cache
    ".ruff_cache",   # Ruff cache
    ".pytest_cache", # Pytest cache
    "notebooks",     # Jupyter notebooks (have many formatting issues)
    ".tmp",          # Temporary files directory
    "docs",          # Documentation directory
    
    # Additional exclusions
    ".mypy_cache",   # Mypy type checker cache
    ".sourcery",     # Sourcery analysis cache
    ".cursor",       # Cursor IDE configuration
    ".vscode",       # VS Code configuration
    ".idea",         # JetBrains IDE configuration
    ".coverage",     # Coverage report files
    ".coverage.*",   # Coverage data files
    "htmlcov",       # HTML coverage reports
    "*.pyc",         # Python compiled files
    "*.pyo",         # Python optimized files
    "*.pyd",         # Python extension modules
    "*.so",          # Shared object files
    "*.dll",         # Dynamic link libraries
    "*.log",         # Log files
    "*.lock",        # Lock files (uv.lock, etc.)
    "justfile",      # Just command runner
    "Makefile",      # Make files
    "*.mk",          # Make include files
    ".env",          # Environment files
    ".env.*",        # Environment variant files
    "*.orig",        # Merge conflict originals
    "*.rej",         # Patch rejection files
    "*.bak",         # Backup files
    "*.swp",         # Vim swap files
    "*.swo",         # Vim swap overflow
    "*~",            # Editor backup files
    ".DS_Store",     # macOS metadata
    "Thumbs.db",     # Windows thumbnail cache
    ".hypothesis",   # Hypothesis testing cache
    ".tox",          # Tox testing environments
    ".nox",          # Nox testing environments
    "site",          # MkDocs build output
    "_build",        # Sphinx build output
    "node_modules",  # Node.js dependencies (if any)
]

[tool.ruff.lint.isort]
known-third-party = ["fastapi", "pydantic"]  # Third-party imports for sorting

[tool.ruff.format]
quote-style = "double"  # Use double quotes for strings
indent-style = "space"  # Use spaces for indentation
skip-magic-trailing-comma = false  # Respect trailing commas
line-ending = "auto"  # Auto-detect line endings

[tool.ty]
# Type checker configuration - add rules as needed

[tool.pytest.ini_options]
pythonpath = ["."]  # Add current directory to Python path
asyncio_mode = "auto"  # Auto-detect async tests
asyncio_default_fixture_loop_scope = "function"  # New event loop per test
addopts = ["--cov", "-p no:warnings", "-s"]  # Coverage, no warnings, no capture
python_files = ["test_*.py"]  # Test file pattern
markers = ["unit", "integration", "performance", "e2e", "slow"]  # Custom test markers


[tool.ruff.lint]
select = ["ALL"]  # Enable all lint rules by default

ignore = [
    "D",  # Ignore all docstring rules
    "COM812",  # Allow trailing comma missing
    "ISC001",  # Allow implicit string concatenation
    "TRY003",  # Allow long messages in exception
    "EM101",  # Allow raw string in exception
    "EM102",  # Allow f-strings in exceptions
    "FIX002",  # Allow TODO comments
    "TD002",  # Allow missing author in TODO
    "TD003",  # Allow missing issue link in TODO
    "E501",  # Allow line too long (handled by formatter)
    "FBT001",  # Allow boolean positional arg in function definition
    "FBT002",  # Allow boolean default value in function definition
    "FBT003",  # Allow boolean positional value in function call
    "TRY300",  # Allow try-except-pass
    "G004",  # Allow logging f-strings
    "ERA001",  # Allow commented-out code
    # Brittle code philosophy ignores
    "S101",    # Assert statements are fine for brittle code
    "BLE001",  # Blind exceptions - you want to fix these manually
    "TRY203",  # Re-raising exceptions
    "T201",    # Print statements (if using rich prints)
    "E402",    # Import order in notebooks
]

[tool.ruff.lint.per-file-ignores]
# Test files - relaxed rules for testing
"**/tests/**/*test*.py" = [
    "F401",    # Allow unused imports, e.g. for pytest
    "ANN001",  # Allow non-typed function arguments
    "ANN002",  # Allow non-typed *args
    "ANN003",  # Allow non-typed **kwargs
    "ARG001",  # Allow unused function arguments
    "ANN201",  # Allow missing return type annotation
    "PLR2004", # Allow magic values in tests
    "S101",    # Allow assert statements in tests
    "S106",    # Allow hard-coded passwords in tests
    "SLF001",  # Allow private member access in tests
    "S105",    # Allow hard-coded passwords in tests
]
# Example files - relaxed rules for examples
"examples/**" = [
    "ANN001",  # Allow non-typed function arguments
    "ANN002",  # Allow non-typed *args
    "ANN003",  # Allow non-typed **kwargs
    "ARG001",  # Allow unused function arguments
    "ANN201",  # Allow missing return type annotation
    "PLR2004", # Allow magic values in examples
    "S101",    # Allow assert statements in examples
    "S106",    # Allow hard-coded passwords in examples
    "SLF001",  # Allow private member access in examples
    "S105",    # Allow hard-coded passwords in examples
    "T201",    # Allow print statements in examples
]
# Doc generation files - allow special naming convention
"src/pfc/doc_gen/d*.py" = [
    "N999",    # Allow non-standard module names (d1_PromptDoc, etc.)
]
# Notebook files - special rules for Jupyter notebooks
"*.ipynb" = [
    "E402",    # Import order in notebooks
    "F811",    # Redefinition of unused names (common in notebooks)
]
# Scripts - allow missing __init__.py
"scripts/*" = [
    "INP001",  # Allow implicit namespace packages
]
# Archived code - ignore all rules
".archive/*" = [
    "ALL",     # Ignore all rules for archived code
]

[tool.ruff.lint.pylint]
# Complexity limits for Pydantic models and functions
max-statements = 50  # Max statements per function/method
max-branches = 12    # Max branches per function
max-args = 7         # Max arguments per function
max-locals = 15      # Max local variables per function
max-public-methods = 20  # Max public methods per class

[tool.ruff.lint.mccabe]
# Limit if statement depth/complexity
max-complexity = 10  # McCabe complexity (nested if depth)
