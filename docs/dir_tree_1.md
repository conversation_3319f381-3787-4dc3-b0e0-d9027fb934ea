├─ pyproject.toml
├─ README.md
├─ notebooks/
├─ .tmp/
└─ src/
   └─ promptflow/
      ├─ __init__.py
      │
      ├─ aa_input_layer/
      │   ├─ __init__.py
      │   └─ problem_description.py
      │
      ├─ bb_core_generation/
      │   ├─ __init__.py
      │   ├─ prompt_generator.py
      │   ├─ test_case_generator.py
      │   ├─ requirements_doc_generator.py
      │   ├─ synthetic_pipeline/
      │   │     ├─ __init__.py
      │   │     └─ synthetic_test_generator.py
      │   └─ iteration_machine_1.py          # loops on prompt only
      │
      ├─ cc_testing_evaluation/
      │   ├─ __init__.py
      │   ├─ test_case_chunker.py
      │   ├─ test_executor.py
      │   ├─ grader_generator.py
      │   └─ test_grader.py
      │
      ├─ dd_refinement/
      │   ├─ __init__.py
      │   ├─ feedback_summary.py
      │   ├─ prompt_refiner.py
      │   └─ iteration_machine_2.py          # fires on TC outputs + expected
      │
      ├─ ee_optimisation/
      │   ├─ __init__.py
      │   ├─ token_optimizer.py
      │   ├─ final_prompt_generation.py
      │   └─ iteration_machine_3.py          # adjusts graders on TC outputs
      │
      ├─ ff_final_delivery/
      │   ├─ __init__.py
      │   └─ final_delivery.py
      │
      ├─ models.py
      ├─ pipeline.py
      ├─ prompts.py
      │
      ├─ utils/
      │   ├─ __init__.py
      │   ├─ cli.py
      │   ├─ display.py
      │   ├─ token_count.py
      │   ├─ adapters/
      │   │     └─ kedro_kestra_langsmith.py
      │   └─ ui/
      │         └─ streamlit_harness.py
      │
      └─ tasks/
          ├─ __init__.py
          └─ ztasks.md
