# Sourcery Refactoring Cheatsheet

_Opus: Based on Sourcery's documentation, here's a comprehensive list of their elegance refactorings that make Python code more pythonic and concise:_

## Pythonic Built-ins & Operators

### Iterator & Collection Patterns

* use-next - Replace for loops with next() for finding first match
* use-any - Replace for loops with any() for existence checks
* use-all - Replace for loops with all() for universal checks
* convert-to-enumerate - Replace manual counter with enumerate()
* use-itertools-product - Replace nested loops with itertools.product()
* use-count - Replace manual counting with collection.count()
* use-len - Replace manual length calculation with len()

### Augmented Operations

* augmented-assign - Use +=, -=, *= instead of x = x + 1
* dict-assign-update-to-union - Use |= for dict updates (Python 3.9+)
* merge-list-append - Combine multiple appends
* merge-list-appends-into-extend - Use extend() instead of multiple append()
* for-append-to-extend - Replace for loop appends with extend()

### String Operations

* use-fstring-for-concatenation - Replace string concatenation with f-strings
* use-fstring-for-formatting - Replace .format() with f-strings
* use-str-join - Replace loops building strings with str.join()
* replace-interpolation-with-fstring - Replace % formatting with f-strings
* simplify-fstring-formatting - Simplify redundant f-string formatting

## Comprehensions & Generators

### List/Dict/Set Comprehensions

* list-comprehension - Convert for loops to list comprehensions
* dictionary-comprehension - Convert for loops to dict comprehensions
* set-comprehension - Convert for loops to set comprehensions
* collection-to-comprehension - General comprehension conversion
* comprehension-to-generator - Convert comprehensions to generators when appropriate
* sum-comprehension - Use sum() with generator expressions

### Generator Patterns

* yield-from - Replace for x in y: yield x with yield from y
* simplify-generator - Simplify generator expressions

## Conditional & Boolean Elegance

### Ternary & Guard Patterns

* assign-if-exp - Use ternary operator x = a if condition else b
* swap-if-expression - Reorder ternary for clarity
* use-or-for-fallback - Use value or default pattern
* guard - Use early returns instead of nested if
* last-if-statement-guard - Convert final if to guard clause

### Boolean Simplifications

* boolean-if-exp-identity - Simplify True if x else False to bool(x)
* simplify-boolean-comparison - Remove redundant boolean comparisons
* or-if-exp-identity - Simplify x or (y if condition else z)
* invert-any-all - Use not any() instead of all(not x for x)

## Collection Operations

### Dictionary Patterns

* default-get - Use dict.get(key, default) instead of if/else
* use-dictionary-items - Use .items() instead of iterating keys
* use-dictionary-union - Use | for dict merging (Python 3.9+)
* missing-dict-items - Suggest .items() when accessing key and value

### List/Set Operations

* collection-into-set - Use set() for membership testing
* list-literal - Use [] instead of list()
* dict-literal - Use {} instead of dict()
* tuple-literal - Use () instead of tuple()
* collection-to-bool - Use truthiness of collections

## Advanced Elegance Patterns

### Unpacking & Assignment

* swap-variable - Use tuple unpacking for swaps: a, b = b, a
* use-assigned-variable - Reuse assigned variables
* use-named-expression - Use walrus operator := (Python 3.8+)
* merge-comparisons - Use in for multiple equality checks

### Function & Method Patterns

* method-chaining - Chain method calls when possible
* inline-immediately-returned-variable - Return directly
* inline-immediately-yielded-variable - Yield directly
* use-contextlib-suppress - Use with suppress(Exception): instead of try/except/pass

### Numeric & Math

* min-max-identity - Simplify min(x, x) to x
* square-identity - Use x ** 2 instead of x * x
* simplify-numeric-comparison - Simplify redundant numeric comparisons
* binary-operator-identity - Remove identity operations like x + 0

## Path & File Operations

* ensure-file-closed - Use with statement for file operations
* pathlib-path-read - Use Path.read_text() instead of open().read()
* use-file-iterator - Iterate files directly instead of .readlines()

## Pandas-Specific

* dataframe-append-to-concat - Use pd.concat() instead of .append()
* pandas-avoid-inplace - Avoid inplace=True operations
* use-iloc - Use .iloc for integer-based indexing
* use-isna - Use .isna() instead of .isnull()

## Control Flow Elegance

* merge-nested-ifs - Combine nested if statements with and
* merge-else-if-into-elif - Use elif instead of else: if
* remove-unnecessary-else - Remove else after return/break/continue
* lift-return-into-if - Move return statements into conditionals
* switch - Suggest match/case for multiple conditions (Python 3.10+)

These refactorings focus on making code more concise, idiomatic, and performant by leveraging Python's built-in features and elegant syntax patterns. The goal is to write code that experienced Python developers would immediately recognize as "Pythonic".
