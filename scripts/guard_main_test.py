#!/usr/bin/env python3
"""
Guard main test script - demonstrates guard main pattern with error handling
"""

import sys
import os
import tempfile
from pathlib import Path

# Global state for undo functionality
_UNDO_STACK = []

def guard_main():
    """Guard to prevent duplicate execution"""
    script_name = Path(__file__).name
    current_dir = Path.cwd()
    
    # Check if script with same name already running
    temp_dir = Path(tempfile.gettempdir())
    lock_file = temp_dir / f"{script_name}.lock"
    
    if lock_file.exists():
        raise RuntimeError(f"Script {script_name} is already running in {current_dir}")
    
    # Create lock file
    lock_file.touch()
    _UNDO_STACK.append(('remove_lock', lock_file))
    
    print(f"Guard main activated for {script_name} in {current_dir}")
    return True

def create_test_file():
    """Create a test file to demonstrate functionality"""
    test_file = Path("test_guard_main.txt")
    test_file.write_text("This is a test file created by guard_main_test.py")
    _UNDO_STACK.append(('remove_file', test_file))
    print(f"Created test file: {test_file}")

def undo_all():
    """Undo all operations in reverse order"""
    print("Undoing all operations...")
    
    # Also check for existing lock files
    script_name = Path(__file__).name
    temp_dir = Path(tempfile.gettempdir())
    lock_file = temp_dir / f"{script_name}.lock"
    
    if lock_file.exists():
        lock_file.unlink()
        print(f"Removed existing lock file: {lock_file}")
    
    while _UNDO_STACK:
        action, target = _UNDO_STACK.pop()
        
        if action == 'remove_lock':
            if target.exists():
                target.unlink()
                print(f"Removed lock file: {target}")
        elif action == 'remove_file':
            if target.exists():
                target.unlink()
                print(f"Removed file: {target}")
    
    print("All operations undone")

def main():
    """Main function"""
    try:
        guard_main()
        create_test_file()
        
        print("Script executed successfully")
        print("Run with 'undo' argument to clean up")
        
    except RuntimeError as e:
        print(f"Error: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}")
        undo_all()
        sys.exit(1)

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "undo":
        undo_all()
    else:
        main()