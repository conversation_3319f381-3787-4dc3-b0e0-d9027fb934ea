#!/bin/bash
# Test script for Cursor Background Agent Docker environment

echo "=== Testing Cursor Docker Environment ==="
echo "This script simulates what the Background Agent would do"
echo ""

# Check if <PERSON><PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker Desktop first."
    exit 1
fi

# Build the Docker image
echo "1. Building Docker image..."
cd .cursor
docker build -t pfc-cursor-env . || {
    echo "❌ Docker build failed"
    exit 1
}

# Run a container and test the environment
echo ""
echo "2. Testing environment setup..."
docker run --rm -v "$(dirname $(pwd))":/workspace pfc-cursor-env bash -c "
    set -e
    echo '✓ Container started successfully'
    
    # Check Python
    echo -n 'Python: '
    python --version
    
    # Check uv
    echo -n 'uv: '
    uv --version || echo 'NOT FOUND'
    
    # Check just
    echo -n 'just: '
    just --version || echo 'NOT FOUND'
    
    # Test the install command from environment.json
    echo ''
    echo '3. Testing install command...'
    cd /workspace
    python -m pytest tests/ --version || echo 'pytest not installed yet'
    
    # Check if dependencies can be installed
    echo ''
    echo '4. Testing dependency installation...'
    uv sync --dev && echo '✓ Dependencies installed successfully' || echo '❌ Dependency installation failed'
    
    # Run the actual install command
    echo ''
    echo '5. Running environment.json install command...'
    python -m pytest tests/ || echo 'Tests not found or failed (this is OK if no tests exist yet)'
"

echo ""
echo "=== Test Complete ==="