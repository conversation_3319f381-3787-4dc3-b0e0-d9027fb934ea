"""refactor-prompts-001 - Prompt templates for architecture-aware refactoring

1. Analysis prompts for finding violations
2. Refactoring prompts for fixing issues  
3. Verification prompts for checking compliance

Why: Consistent, comprehensive refactoring instructions
How: Template-based prompts with rule injection

Requirements:
☑️ Core prompt templates
⏳ Rule-specific examples
⏳ Context injection system
"""

ANALYZE_FOR_FCIS = """
You are analyzing code for FCIS (Functional Core, Imperative Shell) violations.

Look for:
1. Functions that mix I/O with business logic
2. Functions with side effects that should be pure
3. Business logic depending on external state
4. Functions doing both computation and mutation

For each violation found, report:
- File path and line numbers
- What makes it impure (I/O, mutation, external deps)
- How to separate functional core from imperative shell

Example violations:
```python
# BAD - mixes I/O with logic
def calculate_total(order_id):
    order = database.get_order(order_id)  # I/O
    total = sum(item.price for item in order.items)  # logic
    logger.info(f"Total: {total}")  # I/O
    return total

# GOOD - separated
def calculate_total(order_items):  # pure function
    return sum(item.price for item in order_items)

def get_order_total(order_id):  # imperative shell
    order = database.get_order(order_id)
    total = calculate_total(order.items)
    logger.info(f"Total: {total}")
    return total
```
"""

ANALYZE_EXCEPTION_HANDLING = """
Find all exception handling that violates brittle code principles.

Violations to find:
1. try/except blocks outside of main()
2. Defensive programming that should be assertions
3. Silenced exceptions (except: pass)
4. Over-broad exception catching

Report format:
- Location: file:line
- Current code snippet
- Why it violates brittle principles
- Suggested assertion-based replacement

Example:
```python
# BAD - defensive exception handling
def process_data(data):
    try:
        result = transform(data)
    except ValueError:
        return None  # silently fails
        
# GOOD - brittle with assertions
def process_data(data):
    assert data is not None, "Data required"
    assert len(data) > 0, "Data cannot be empty"
    return transform(data)  # fails fast on issues
```
"""

REFACTOR_TO_FCIS = """
Refactor this code to follow FCIS architecture:

Rules:
1. Extract ALL I/O operations to imperative shell
2. Make core functions pure (no side effects)
3. Core functions only transform data
4. Shell orchestrates and handles I/O

Pattern to follow:
```python
# Functional Core
def business_logic(input_data):
    # Pure transformation
    return processed_data

# Imperative Shell  
def main():
    # Get data (I/O)
    data = read_input()
    
    # Process (pure)
    result = business_logic(data)
    
    # Output (I/O)
    write_output(result)
```

Keep changes minimal while achieving separation.
"""

REFACTOR_PYDANTIC_COMPLIANCE = """
Fix Pydantic models to comply with the 10 critical rules:

1. Add strict=True to all models
2. Remove Optional from intermediate models  
3. Add validate_assignment=True
4. Set extra="forbid"
5. Convert asserts to field validators
6. Fix model_dump() calls to use mode="json"
7. Add frozen=True for config objects
8. Add __schema_version__ attribute
9. Add doctest examples in docstring
10. Split models > 25 lines

Example fix:
```python
# Before
class UserData(BaseModel):
    name: Optional[str]
    age: int
    
# After  
class UserData(BaseModel):
    model_config = ConfigDict(
        strict=True,
        validate_assignment=True,
        extra="forbid"
    )
    __schema_version__ = "v1"
    
    name: str  # No Optional in intermediate model
    age: int
    
    @field_validator('age')
    def validate_age(cls, v):
        if v < 0:
            raise ValueError("Age must be positive")
        return v
```
"""

VERIFY_REFACTORING = """
Verify the refactored code against architecture rules:

Checklist:
□ FCIS: Business logic is pure functions only
□ FCIS: I/O only in imperative shell/main()
□ Brittle: No try/except in business logic
□ Brittle: Assertions for all preconditions
□ Clarity: Helper functions max 2 levels deep
□ Clarity: Function names are self-explanatory
□ Pydantic: All models in strict mode
□ Pydantic: No Optional in intermediate models
□ Docs: Pyramid principle docstrings
□ Docs: EOL comments explain WHY

For any remaining issues, explain why they can't be fixed without breaking functionality.
"""


def build_file_analysis_prompt(file_path: str, rules: list[str]) -> str:
    """Build comprehensive analysis prompt for a file"""
    rule_prompts = {
        "fcis": ANALYZE_FOR_FCIS,
        "exceptions": ANALYZE_EXCEPTION_HANDLING,
        "pydantic": "Check all Pydantic models against the 10 rules",
        "documentation": "Check for pyramid principle docstrings"
    }

    selected_prompts = [rule_prompts.get(r, "") for r in rules]

    return f"""
Analyze {file_path} for architecture violations.

{' '.join(selected_prompts)}

Provide results in JSON format:
{{
    "violations": [
        {{
            "line": 10,
            "rule": "fcis",
            "description": "Mixes I/O with business logic",
            "severity": "high"
        }}
    ]
}}
"""
