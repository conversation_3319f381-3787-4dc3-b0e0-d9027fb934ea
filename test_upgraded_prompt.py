#!/usr/bin/env python3
"""Test the upgraded prompt function with response_format"""

from pfc.core.llms import prompt
from pfc.core.models import ScenarioDoc
import json

# Test 1: Basic structured output
print("=== Test 1: Basic Structured Output ===")
response = prompt(
    "Generate a simple JSON object with name and age fields",
    model="gpt-4o-mini",
    response_format={
        "type": "json_schema",
        "json_schema": {
            "name": "person",
            "schema": {
                "type": "object",
                "properties": {
                    "name": {"type": "string"},
                    "age": {"type": "integer"}
                },
                "required": ["name", "age"],
                "additionalProperties": False
            }
        }
    }
)

print(f"Response: {response}")
parsed = json.loads(response)
print(f"Parsed: {parsed}")

# Test 2: ScenarioDoc schema
print("\n=== Test 2: ScenarioDoc Schema ===")
schema = ScenarioDoc.model_json_schema()
print(f"Schema keys: {list(schema.keys())}")
print(f"Schema properties: {list(schema.get('properties', {}).keys())}")

print("\n✅ Upgraded prompt() with response_format works!")